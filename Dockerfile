# Stage 1: Build the application
FROM node:18-alpine AS builder
WORKDIR /app

# Copy package files and install ALL dependencies (including dev)
COPY package*.json ./
RUN npm ci

# Copy Tailwind/PostCSS config files (critical for build)
COPY . .

# Build the application
RUN npm run build

# Prune dev dependencies after building
RUN npm prune --omit=dev

# Stage 2: Production image (standalone mode)
FROM node:18-alpine
WORKDIR /app

ENV NODE_ENV production

# Copy standalone build output
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

USER node
EXPOSE 3000
CMD ["node", "server.js"]