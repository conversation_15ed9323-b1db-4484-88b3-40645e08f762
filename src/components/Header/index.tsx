"use client";
import { usePathname } from "next/navigation";
import { useEffect, useState, useRef } from "react";
import Image from "next/image";
import Button from "@/containers/Button";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import Link from "next/link";
import InquiryDialog from "../Inquiry/InguiryDialog";

export default function Header() {
  const pathname = usePathname();
  const [open, setOpen] = useState(false);
  const targetRef = useRef<string | null>(null);

  const baseClass =
    "font-poppins px-4 py-[0.75rem] text-[1rem]  text-[#C6C6C6] leading-[1.5rem] tracking-[0.02rem]   uppercase transition-colors  lg:px-[2rem]";

  const handleLinkClick = (label: string) => {
    targetRef.current = label === "HOME" ? null : label.toLowerCase();
    setOpen(false);
  };

  useEffect(() => {
    if (!open && targetRef.current !== null) {
      const timeout = setTimeout(() => {
        const section = document.getElementById(targetRef.current!);
        if (section) {
          section.scrollIntoView({ behavior: "smooth" });
        }
        targetRef.current = null;
      }, 500);

      return () => clearTimeout(timeout);
    }
  }, [open]);

  return (
    <nav className="absolute top-0 z-10 container mt-[1.75rem] w-full bg-transparent">
      <div className="mx-auto flex items-center justify-between px-5 py-[0.75rem] lg:px-[6.25rem]">
        <div className="flex-shrink-0">
          <Image
            src="/images/logo.svg"
            alt="Logo"
            width={0}
            height={0}
            className="h-auto w-[5.9rem] object-contain"
          />
        </div>

        <ul className="poppins-semibold hidden items-center justify-center md:hidden lg:flex lg:gap-3 xl:gap-[1.25rem]">
          <li className="relative">
            <Link
              href="/"
              className={`${baseClass} ${pathname === "/" ? "text-white" : "text-[#C6C6C6] hover:text-white"}`}
            >
              Home
            </Link>
            {pathname === "/" && (
              <span className="absolute top-full left-1/2 mt-1 h-2 w-2 -translate-x-1/2 rounded-full bg-[#9fd183]" />
            )}
          </li>

          <li>
            <Link
              href="/#about"
              onClick={() => {
                const section = document.getElementById("about");
                if (section) section.scrollIntoView({ behavior: "smooth" });
              }}
              className={`${baseClass} ${pathname === "/about" ? "text-white" : "text-[#C6C6C6] hover:text-white"}`}
            >
              ABOUT
            </Link>
          </li>

          {/* <li>
            <Link
              href="/production"
              className={`${baseClass} ${
                pathname === "/production"
                  ? "!bg-white !text-[#49772F]"
                  : "text-white hover:text-gray-300"
              }`}
            >
              PRODUCTION
            </Link>
          </li> */}
          <li className="relative">
            <Link
              href="/production"
              className={`${baseClass} ${pathname === "/production" ? "text-white" : "text-[#C6C6C6] hover:text-white"}`}
            >
              Production
            </Link>
            {pathname === "/production" && (
              <span className="absolute top-full left-1/2 mt-1 h-2 w-2 -translate-x-1/2 rounded-full bg-[#9fd183]" />
            )}
          </li>

          {/* <li>
            <Link
              href="/contact"
              className={`${baseClass} ${
                pathname === "/contact"
                  ? "!bg-white !text-[#49772F]"
                  : "text-white hover:text-gray-300"
              }`}
            >
              CONTACT
            </Link>
          </li> */}
          <li className="relative">
            <Link
              href="/contact"
              className={`${baseClass} ${pathname === "/contact" ? "text-white" : "text-[#C6C6C6] hover:text-white"}`}
            >
              Contact
            </Link>
            {pathname === "/contact" && (
              <span className="absolute top-full left-1/2 mt-1 h-2 w-2 -translate-x-1/2 rounded-full bg-[#9fd183]" />
            )}
          </li>
        </ul>

        {/* Desktop Button */}
        <div className="ml-[1rem] hidden items-center justify-center lg:flex">
          <InquiryDialog
            trigger={
              <Button className="transition hover:bg-gray-200 hover:text-black lg:flex">
                INQUIRE NOW
              </Button>
            }
          />
        </div>

        {/* Mobile Drawer */}
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger asChild>
            <button className="block text-white lg:hidden">
              <span
                className="material-symbols-outlined text-[3rem]"
                style={{ fontSize: "60px" }}
              >
                menu
              </span>
            </button>
          </DrawerTrigger>

          <DrawerContent className="h-full items-center justify-center bg-[#3B1F16] px-6 py-10 text-white lg:hidden">
            <button
              onClick={() => setOpen(false)}
              className="absolute top-9 right-5 text-white"
            >
              <span className="material-symbols-outlined text-4xl">close</span>
            </button>

            <div className="poppins-semibold flex max-w-md flex-col items-center gap-6">
              <Image
                src="/images/logo.svg"
                alt="Logo"
                width={94}
                height={40}
                className="mb-6 object-contain"
              />

              {["HOME", "ABOUT", "PRODUCTION", "DONATE", "CONTACT"].map(
                (label, i) => (
                  <button
                    key={i}
                    onClick={() => handleLinkClick(label)}
                    className="py-3 text-lg uppercase"
                  >
                    {label}
                  </button>
                ),
              )}
              <InquiryDialog
                trigger={
                  <Button className="transition hover:bg-gray-200 hover:text-black lg:flex">
                    INQUIRE NOW
                  </Button>
                }
              />
            </div>
          </DrawerContent>
        </Drawer>
      </div>
    </nav>
  );
}
