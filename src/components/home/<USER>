"use client";

import Image from "next/image";
import Header from "@/components/Header";

export default function LandingHero() {
  return (
    <>
      <header className="relative flex h-auto min-h-screen w-full items-center justify-center overflow-hidden">
        <Header />

        <Image
          src="/images/landingPage.svg"
          alt="Coffee Plant Background"
          layout="fill"
          objectFit="cover"
          objectPosition="center"
          priority
        />

        <div className="relative z-10 container w-full items-center justify-center px-4 text-center text-white md:px-6">
          <h1 className="cormorant-font container text-center text-5xl leading-normal text-white [--webkit-text-stroke-color:#FFF] [--webkit-text-stroke-width:0.5px] [text-shadow:0px_4px_4px_rgba(0,0,0,0.25)] md:text-[4rem] md:leading-[5.2rem]">
            From Nepal’s Hills to Your Cup:
            <br />
            Premium Hand-Harvested Coffee Beans
          </h1>
        </div>
        <button className="absolute bottom-5 left-1/2 h-20 w-20 -translate-x-1/2 text-white">
          <span className="material-symbols-outlined arrow-bounce text-8xl leading-none font-light tracking-wider">
            arrow_downward
          </span>
        </button>
      </header>
    </>
  );
}
