import React from "react";
import Image from "next/image";
import { logos } from "@/constants/client";
export default function Clients() {
  return (
    <section
      id="clients"
      className="relative z-10 w-full overflow-hidden bg-[#49772F] px-6 py-24 md:px-24 md:py-24"
    >
      <div className="flex flex-col items-center justify-center gap-20">
        <h2 className="cormorant-font text-center text-[3.75rem] leading-[90%] font-bold text-white">
          Clients
        </h2>

        <div className="flex flex-col  justify-center gap-10 md:flex-row lg:gap-16">
          {logos.map(({ src, alt }, index) => (
            <div key={index} className="flex flex-col">
              <Image
                src={src}
                alt={alt}
                width={0}
                height={0}
                className="h-20 w-32 object-contain lg:h-full lg:w-full"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
