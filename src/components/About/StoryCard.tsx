"use client";
import Image from "next/image";
import { useState } from "react";
import StoryDialog from "./StoryDialog";
import Button from "@/containers/Button";
interface StoryCardProps {
  src: string;
  alt: string;
  title: string;
  description: string;
}

export default function StoryCard({
  src,
  alt,
  title,
  description,
}: StoryCardProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className="group relative flex h-[33.08rem] w-full cursor-pointer items-center justify-center overflow-hidden bg-cover bg-center transition-transform duration-500 ease-in-out hover:scale-105 md:h-[37.5rem]">
        <Image
          src={src}
          alt={alt}
          width={400}
          height={600}
          className="h-full w-full object-cover brightness-150 grayscale transition-all duration-500 group-hover:grayscale-0"
        />

        <div className="absolute inset-0 flex flex-col items-center justify-center gap-4 opacity-0 group-hover:opacity-100">
          <p className="font-cormorant text-[4rem] leading-[100%] font-semibold text-white uppercase">
            {title}
          </p>
          <Button
            onClick={() => setIsOpen(true)}
            className="cursor-pointer text-white opacity-0 group-hover:opacity-100 hover:bg-white hover:text-black"
          >
            Learn More
          </Button>
        </div>
      </div>

      <StoryDialog
        title={title}
        description={description}
        src={src}
        open={isOpen}
        onOpenChange={setIsOpen}
      />
    </>
  );
}
