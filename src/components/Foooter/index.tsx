import Image from "next/image";
import Link from "next/link";

export default function Footer() {
  return (
    <div className="relative z-10 w-full bg-[#F5EDBE]">
      <div className="mx-auto flex max-w-[90rem] flex-col items-center gap-6 px-4 py-12 sm:px-6 lg:px-16">
        <Image
          src="/images/footerImage.png"
          alt="Logo"
          width={135}
          height={145}
          className="aspect-[135/145.97] h-auto w-[8.4375rem] object-contain"
          priority
          quality={100}
        />
        <ul className="poppins-regular flex w-full flex-col flex-wrap items-start justify-start gap-x-6 gap-y-2 text-left text-[#484848] md:flex-row md:items-center md:justify-center md:text-center">
          <li>
            <Link href="#about" className="nav-link">
              ABOUT
            </Link>
          </li>
          <li>
            <Link href="#production" className="nav-link">
              PRODUCTION
            </Link>
          </li>
          {/* <li>
            <Link href="#donate" className="nav-link">
              DONATE
            </Link>
          </li> */}
          <li>
            <Link href="#contact" className="nav-link">
              CONTACT
            </Link>
          </li>
          <li>
            <Link href="#plantyourCoffe" className="nav-link">
              PLANT YOUR OWN COFFEE
            </Link>
          </li>
        </ul>
      </div>

      <footer className="poppins-regular flex w-full flex-col items-center justify-between bg-[#49772F] px-20 py-4 text-sm text-white md:flex-row lg:text-base">
        <p>Copyrights @2025 | All Rights Reserved.</p>
        <p>
          Designed & Developed by
          <span className="mx-2.5">|</span>
          <a
            type="button"
            href="https://optisyslab.com/"
            target="_blank"
            className="poppins-semibold text-white"
          >
            Optisys Lab
          </a>
        </p>
      </footer>
    </div>
  );
}
