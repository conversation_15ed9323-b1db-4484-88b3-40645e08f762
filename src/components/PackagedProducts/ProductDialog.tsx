import {
  Dialog,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import Image from "next/image";

interface StoryModalProps {
  title: string;
  description: string;
  src: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ProductDialog: React.FC<StoryModalProps> = ({
  title,
  description,
  src,
  open,
  onOpenChange,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex h-[80%] w-[80%] flex-col gap-4 overflow-y-auto border-none bg-white p-4 shadow-lg md:max-h-[92%] md:max-w-[95%] md:flex-row md:gap-8 md:p-[3.75rem]">
        <div className="flex h-[50%] w-full justify-center md:h-auto md:w-[50%]">
          <Image
            src={src}
            alt={title}
            width={0}
            height={0}
            className="w-full object-cover md:h-auto md:max-w-[25rem]"
          />
        </div>

        <div className="h-auto w-full">
          <DialogHeader>
            <DialogTitle className="cormorant-font mb-4 text-[1.5rem] text-[#49772F] uppercase sm:text-[2rem] md:text-[3rem] lg:text-[4rem]">
              {title}
            </DialogTitle>
            <DialogDescription className="poppins-regular flex flex-col space-y-4 text-sm leading-[1.5rem] tracking-[0.02rem] text-black md:text-base">
              {description.split("\n").map((paragraph, index) => (
                <p key={index}>{paragraph}</p>
              ))}
              <div className="mt-10">
                <button className="poppins-regular flex h-12 items-center justify-center gap-2 bg-[#3a1f1f] px-4 font-medium tracking-[0.02rem] text-white uppercase transition hover:bg-[#2d1818]">
                  INQUIRE NOW
                </button>
              </div>
            </DialogDescription>
          </DialogHeader>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProductDialog;
