"use client";
import React, { useState } from "react";
import Image from "next/image";
import Button from "@/containers/Button";

import ProductDialog from "./ProductDialog";

export default function PackagedProducts() {
  const products = ["Washed, Illam", "Natural, Illam", "Natural, Chitwan"];
  const [activeIndex, setActiveIndex] = useState(0);
 
  const [selectedProduct, setSelectedProduct] = useState<{
    title: string;
    src: string;
  } | null>(null);

  return (
    <section
      id="packagedProducts"
      className="relative z-10 w-full bg-[#49772F] px-6 py-24 md:px-16"
    >
      <div className="container mb-12 text-center">
        <p className="poppins-regular text-[1rem] leading-7 tracking-[0.08rem] text-white uppercase md:text-[1.25rem] md:leading-8 md:tracking-[0.1rem]">
          Our Featured Products
        </p>
        <h2 className="cormorant-font text-[3.5rem] leading-normal font-bold text-white uppercase md:text-[6rem]">
          Packaged Products
        </h2>
      </div>

      <div className="flex flex-col items-center md:hidden">
        <div className="flex w-full justify-center transition-all duration-500 ease-in-out">
          <div className="flex w-full max-w-sm flex-col items-center px-4 py-6">
            <Image
              src="/images/product.png"
              alt={products[activeIndex]}
              width={235}
              height={337}
              className="drop-shadow-[0_0_80px_#92dd67] transition duration-300 ease-in-out"
            />
            <p className="cormorant-font pt-6 text-center text-[2rem] text-white">
              {products[activeIndex]}
            </p>
            <Button className="mt-4 px-[2.5rem] text-[1rem] hover:bg-white hover:text-black">
              Learn More
            </Button>
          </div>
        </div>

        <div className="mt-6 flex gap-3">
          {products.map((_, index) => (
            <button
              key={index}
              onClick={() => setActiveIndex(index)}
              className={`h-4 w-4 rounded-full border-1 border-white ${
                activeIndex === index ? "bg-[#3B231B]" : "bg-[#49772F]"
              } transition duration-300`}
            />
          ))}
        </div>
      </div>

      <div className="mx-auto hidden max-w-[90rem] grid-cols-2 gap-8 md:grid lg:grid-cols-3">
        {products.map((title, index) => (
          <div key={index} className="group flex flex-col items-center">
            <Image
              src="/images/products.svg"
              alt={title}
              width={235}
              height={337}
              className="transition duration-300 ease-in-out hover:brightness-110 hover:drop-shadow-[0_0_80px_#92dd67]"
            />
            <p className="cormorant-font pt-6 text-center text-[2rem] text-white md:text-[2.5rem]">
              {title}
            </p>
            <Button
              onClick={() =>
                setSelectedProduct({
                  title,
                  src: "/images/products.svg",
                })
              }
              className="mt-4 px-[2.5rem] text-[1rem] text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100 hover:bg-white hover:text-black"
            >
              Learn More
            </Button>
          </div>
        ))}
        {selectedProduct && (
          <ProductDialog
            title={selectedProduct.title}
            description="Lorem ipsum dolor sit amet consectetur. Orci ac tincidunt scelerisque id consectetur in tristique dictum viverra. Tincidunt amet sed congue neque eu hendrerit elit mauris. Massa dolor porta nunc eget turpis amet. Dui in lectus vitae aliquet."
            src={selectedProduct.src}
            open={!!selectedProduct}
            onOpenChange={(open) => {
              if (!open) setSelectedProduct(null);
            }}
          />
        )}
      </div>
    </section>
  );
}
