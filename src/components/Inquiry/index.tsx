import React from "react";
import Image from "next/image";
import Button from "@/containers/Button";
import InquiryDialog from "./InguiryDialog";

export default function Inquiry() {
  return (
    <section
      id="contact"
      className="relative z-50 h-[50rem] w-full overflow-hidden bg-[#F5EDBE]"
    >
      <div className="absolute inset-0 z-0 flex h-full w-full justify-center xl:justify-start">
        <Image
          src="/images/coffeBeans.svg"
          alt="Coffee Plant"
          width={1920}
          height={800}
          className="h-full w-full object-cover sm:w-auto"
          priority
        />

        <Image
          src="/images/coffeBeans.svg"
          alt="Coffee Plant Mirrored"
          width={1920}
          height={800}
          className="hidden h-full w-auto scale-x-[-1] xl:block"
          priority
        />
      </div>

      <div className="relative z-50 flex h-full min-h-[60vh] w-full items-center justify-center px-5 py-20 sm:px-8 md:px-16 lg:px-44">
        <div className="flex max-w-6xl flex-col items-center justify-center gap-8 text-center">
          <h1 className="cormorant-font text-center text-5xl leading-tight font-bold tracking-wide text-white [--webkit-text-stroke-color:#FFF] [--webkit-text-stroke-width:0.5px] [text-shadow:0px_4px_4px_rgba(0,0,0,0.25)] md:text-8xl md:leading-[6.31rem]">
            Learn About Our Process on Your Own
          </h1>

          <InquiryDialog
            trigger={
              <Button className="w-72 rounded border border-white bg-transparent px-5 py-4 text-[1rem] text-white hover:bg-white hover:text-black">
                Inquire Now
              </Button>
            }
          />
          <div className="flex items-center gap-6">
            <a
              href="https://www.facebook.com/sherpasips/"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Facebook"
              className="rounded-full bg-[#49772F] p-2 transition hover:bg-[#44261B]"
            >
              <Image
                src="/images/fb.svg"
                alt="Facebook"
                width={24}
                height={24}
                className="h-6 w-6"
              />
            </a>

            <a
              href="https://www.instagram.com/sherpasips/"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Instagram"
              className="rounded-md bg-[#49772F] p-2 transition hover:bg-[#44261B]"
            >
              <Image
                src="/images/insta.svg"
                alt="Instagram"
                width={24}
                height={24}
                className="h-6 w-6"
              />
            </a>

            <a
              href="https://www.linkedin.com/sherpasips/"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="LinkedIn"
              className="rounded-md bg-[#49772F] p-2 transition hover:bg-[#44261B]"
            >
              <Image
                src="/images/linkedin.svg"
                alt="LinkedIn"
                width={24}
                height={24}
                className="h-6 w-6"
              />
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
