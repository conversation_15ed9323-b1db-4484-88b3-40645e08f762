"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";

import InquiryForm from "./InquiryForm";

interface InquiryDialogProps {
  trigger: React.ReactNode;
}

export default function InquiryDialog({ trigger }: InquiryDialogProps) {
  const [showDialog, setShowDialog] = React.useState(false);
  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="flex h-auto max-h-[85%] w-[80%] flex-col gap-12 overflow-y-auto border-none bg-white p-14 shadow-lg md:max-w-[60%] md:gap-8 md:p-[3.75rem]">
        <DialogHeader>
          <DialogTitle className="cormorant-font text-4xl font-bold text-green-900">
            INQUIRE
          </DialogTitle>
          <p className="poppins-regular mt-2 text-[1rem] leading-[170%] text-gray-600">
            Tell us how we can help. Whether you’re a coffee lover or looking to
            sell or distribute our products, we’re excited to connect.
          </p>
        </DialogHeader>
        <InquiryForm setShowDialog={setShowDialog} />
      </DialogContent>
    </Dialog>
  );
}
