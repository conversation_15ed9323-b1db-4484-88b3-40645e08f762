"use client";
import React from "react";
interface InquiryFormProps {
  setShowDialog: React.Dispatch<React.SetStateAction<boolean>>;
}
export default function InquiryForm({ setShowDialog }: InquiryFormProps) {
  const [inquiryType, setInquiryType] = React.useState("personal");
  const [formData, setFormData] = React.useState({
    name: "",
    email: "",
    phone: "",
    message: "",
    preferredContact: "email",
    agree: false,
  });
  const [showSuccess, setShowSuccess] = React.useState(false);

  const [errors, setErrors] = React.useState<{ [key: string]: string }>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Invalid email format";
    if (!formData.message.trim()) newErrors.message = "Message is required";
    if (!formData.agree) newErrors.agree = "You must agree to be contacted";

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      console.log("Form submitted:", formData);
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
      setShowDialog(true);
      setFormData({
        name: "",
        email: "",
        phone: "",
        message: "",
        preferredContact: "email",
        agree: false,
      });
    }
  };
  return (
    <form className="gap-5 space-y-4" onSubmit={handleSubmit}>
      {showSuccess && (
        <div className={`bg-[#5f973e] w-1/2 rounded-2xl p-3 success-message poppins-semibold tracking-widest`}>
          Form submitted successfully!
        </div>
      )}
      <div className="poppins-regular grid grid-cols-1 gap-5 text-sm md:grid-cols-3">
        <div className="flex flex-col">
          <label htmlFor="name" className="mb-1 text-sm text-gray-700">
            Name <span className="text-red-600">*</span>
          </label>
          <input
            id="name"
            name="name"
            type="text"
            required
            placeholder="Enter your name"
            className={`rounded border p-2 ${errors.name ? "border-red-500" : ""}`}
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          />
          {errors.name && (
            <span className="mt-1 text-sm text-red-500">{errors.name}</span>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="email" className="mb-1 text-sm text-gray-700">
            Email <span className="text-red-600">*</span>
          </label>
          <input
            id="email"
            name="email"
            type="email"
            required
            placeholder="Enter your email"
            className={`rounded border p-2 ${errors.email ? "border-red-500" : ""}`}
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
          />
          {errors.email && (
            <span className="mt-1 text-sm text-red-500">{errors.email}</span>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="phone" className="mb-1 text-sm text-gray-700">
            Phone Number
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            placeholder="Enter your phone number"
            className="rounded border p-2"
            value={formData.phone}
            onChange={(e) =>
              setFormData({ ...formData, phone: e.target.value })
            }
          />
        </div>
      </div>

      <div>
        <label className="poppins-regular mb-2 block text-sm text-gray-700">
          Inquire for <span className="text-red-600">*</span>
        </label>
        <div className="flex flex-col gap-2 lg:gap-5 xl:flex-row">
          {["personal", "business", "distributor/retailer"].map((type) => (
            <button
              type="button"
              key={type}
              onClick={() => setInquiryType(type)}
              className={`poppins-semibold rounded-full border px-8 py-3 text-base leading-[100%] tracking-wide uppercase ${
                inquiryType === type
                  ? "border-[#49772F] bg-white text-[#49772F]"
                  : "border-none text-[#616161]"
              }`}
              aria-pressed={inquiryType === type}
            >
              {type === "personal"
                ? "Personal Use"
                : type === "business"
                  ? "For Business"
                  : "Distributor/Retailer"}
            </button>
          ))}
        </div>
      </div>

      <div className="poppins-regular flex flex-col text-sm">
        <label htmlFor="message" className="mb-1 text-gray-700">
          Tell us what you’re looking for{" "}
          <span className="text-red-600">*</span>
        </label>
        <textarea
          id="message"
          name="message"
          required
          placeholder="Tell us what you're looking for"
          className={`h-24 w-full rounded border p-2 ${errors.message ? "border-red-500" : ""}`}
          value={formData.message}
          onChange={(e) =>
            setFormData({ ...formData, message: e.target.value })
          }
        />
        {errors.message && (
          <span className="mt-1 text-sm text-red-500">{errors.message}</span>
        )}
      </div>

      <div>
        <label className="poppins-regular text-sm">
          Preferred contact method <span className="text-red-600">*</span>
        </label>
        <div className="poppins-regular mt-1 flex flex-col flex-wrap gap-1 text-sm">
          {[
            { id: "contact-email", label: "Email", value: "email" },
            { id: "contact-phone", label: "Phone", value: "phone" },
            { id: "contact-whatsapp", label: "WhatsApp", value: "whatsapp" },
          ].map(({ id, label, value }) => (
            <label key={id} className="flex items-center gap-2" htmlFor={id}>
              <input
                type="radio"
                id={id}
                name="contact"
                value={value}
                checked={formData.preferredContact === value}
                onChange={(e) =>
                  setFormData({ ...formData, preferredContact: e.target.value })
                }
                className="accent-[#208661]"
              />
              {label}
            </label>
          ))}
        </div>
      </div>

      <div className="poppins-regular text-sm">
        <label htmlFor="agree" className="flex items-center gap-2">
          <input
            type="checkbox"
            id="agree"
            name="agree"
            checked={formData.agree}
            onChange={(e) =>
              setFormData({ ...formData, agree: e.target.checked })
            }
          />
          I agree to be contacted by Sherpa Sips
        </label>
        {errors.agree && (
          <span className="mt-1 text-sm text-red-500">{errors.agree}</span>
        )}
      </div>

      <div className="mt-12 flex items-center justify-center">
        <button
          type="submit"
          className="poppins-semibold rounded bg-[#341F17] px-6 py-2 text-white hover:bg-[#2d1b14]"
        >
          SEND INQUIRY
        </button>
      </div>
      
    </form>
  );
}
