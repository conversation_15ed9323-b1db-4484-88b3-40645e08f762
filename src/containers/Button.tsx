import React from "react";

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

const Button = ({ children, onClick, className = "" }: ButtonProps) => {
  const isWhiteBg = className.includes("bg-white");

  const textColor = isWhiteBg ? "text-black" : "text-white";

  return (
    <button
      type="button"
      onClick={onClick}
      className={`poppins-bold flex h-[3rem] items-center justify-center gap-[0.625rem] border border-white px-4 py-2 text-sm tracking-[0.02rem] leading-[100%] uppercase transition duration-300 ease-in-out lg:px-4 lg:py-4 lg:text-base ${textColor} ${className}`}
    >
      {children}
    </button>
  );
};

export default Button;
