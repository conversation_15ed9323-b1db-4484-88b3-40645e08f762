"use client";

import Image from "next/image";
import { useEffect } from "react";

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Global error caught:", error);
  }, [error]);

  return (
    <div 
      className="min-h-screen w-full flex flex-col items-center justify-center relative"
      style={{
        backgroundImage: "url('/images/errorbg.jpg')",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat"
      }}
    >
      {/* Overlay for better contrast if needed */}
      <div className="absolute inset-0 bg-black/20"></div>
      
      {/* Content container */}
      <div className="relative z-10 flex flex-col items-center justify-center space-y-8 px-4">
        {/* Rocket SVG */}
        <div className="flex items-center justify-center">
          <Image
            src="/images/rocket.svg"
            alt="Rocket"
            width={120}
            height={120}
            className="w-24 h-24 md:w-32 md:h-32 lg:w-40 lg:h-40 rocket-crash"
            priority
          />
        </div>
        
        {/* Error text SVG */}
        <div className="flex items-center justify-center">
          <Image
            src="/images/errortext.svg"
            alt="Error Text"
            width={300}
            height={80}
            className="w-80 h-20 md:w-96 md:h-24 lg:w-[1100px] lg:h-32"
            priority
          />
        </div>
        
        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mt-8">
          <button
            onClick={reset}
            className="inline-flex items-center px-6 py-3 bg-white/90 hover:bg-white text-gray-800 font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            <svg 
              className="w-5 h-5 mr-2" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
              />
            </svg>
            Try Again
          </button>
          
          <button
            onClick={() => window.location.href = '/'}
            className="inline-flex items-center px-6 py-3 bg-transparent border-2 border-white/90 hover:bg-white/10 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            <svg 
              className="w-5 h-5 mr-2" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" 
              />
            </svg>
            Go Home
          </button>
        </div>
      </div>
    </div>
  );
}
