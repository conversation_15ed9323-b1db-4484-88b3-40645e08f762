import Footer from "@/components/Foooter";
import Header from "@/components/Header";
import Inquiry from "@/components/Inquiry";
import PlantCoffeeSection from "@/components/PlantCoffee.tsx";
import Image from "next/image";

export default function Production() {
  return (
    <>
      <div className="fixed top-0 z-0 h-screen w-screen bg-[url('/images/pattern.svg')] bg-cover bg-top opacity-20" />
      <div className="z-10 flex w-full items-center justify-center overflow-hidden bg-[#3B231B] pt-25">
        <Header />
        <div className="relative z-10 mx-5 flex max-w-full flex-col px-4 py-[4rem] text-white md:px-8 md:py-[6.25rem] lg:px-[12.81rem]">
          <p className="poppins-regular text-center text-base tracking-[0.1rem] uppercase md:text-[1.25rem]">
            Our Production Process
          </p>
          <h2 className="cormorant-font mb-12 text-center text-[2.5rem] leading-tight tracking-wide md:mb-16 md:text-[4rem] lg:text-[6rem]">
            PRODUCTION
          </h2>

          {/* Cultivation & Planting */}
          <div className="relative flex flex-col-reverse items-center justify-center md:flex-row-reverse">
            <div className="flex w-1/2 justify-center">
              <Image
                src="/images/planting.svg"
                alt="Cultivation & Planting"
                width={300}
                height={300}
                className="h-auto max-w-full object-contain"
                quality={100}
              />
            </div>
            <div className="w-full max-w-full">
              <h3 className="cormorant-font mb-2 items-center text-[2rem] md:text-[2.5rem]">
                Cultivation & Planting
              </h3>
              <ul className="poppins-regular list-outside list-disc pl-6 text-base leading-[170%]">
                <li>
                  Select high-altitude, well-drained land with a suitable
                  climate.
                </li>
                <li>
                  Grow coffee saplings from coffee seeds in our own nursery for
                  6-8 months.
                </li>
                <li>
                  Transplant those saplings into poly pot bags and care them for
                  2-3 months.
                </li>
                <li>
                  Plant those saplings in the ground that we have prepared,
                  equipped with organic fertilizers and pest control ideas.
                </li>
              </ul>
            </div>
          </div>
          <div className="my-12 h-[2px] w-full bg-[#D0D0D0]"></div>

          {/* Growth & Harvesting */}
          <div className="relative flex flex-col items-center gap-12 md:flex-row">
            <div className="flex w-full justify-between">
              <Image
                src="/images/harvesting.svg"
                alt="Growth & Harvesting"
                width={300}
                height={300}
                className="h-auto max-w-full object-contain"
                quality={100}
              />
            </div>
            <div className="w-full max-w-full">
              <h3 className="cormorant-font mb-2 text-[2rem] md:text-[2.5rem]">
                Growth & Harvesting
              </h3>
              <ul className="poppins-regular list-outside list-disc pl-6 text-base leading-[170%]">
                <li>Coffee plants take 3–4 years to bear fruit.</li>
                <li>
                  Monitor plants as well as trim its branches for proper
                  flowering and fruit development.
                </li>
                <li>
                  Harvest cherries by hand when they turn deep red (selective
                  picking ensures quality).
                </li>
              </ul>
            </div>
          </div>
          <div className="my-12 h-[2px] w-full bg-[#D0D0D0]"></div>

          {/* Processing */}
          <div className="relative flex flex-col-reverse items-center md:flex-row-reverse">
            <div className="flex w-full justify-center md:justify-end">
              <Image
                src="/images/processing.svg"
                alt="Processing"
                width={300}
                height={300}
                className="h-auto max-w-full object-contain"
                quality={100}
              />
            </div>
            <div className="w-full max-w-full">
              <h3 className="cormorant-font mb-2 text-[2rem] md:text-[2.5rem]">
                Processing
              </h3>
              <ul className="poppins-regular list-outside list-disc pl-6 text-base leading-[170%]">
                <li>
                  Dry Method: Cherries are sun-dried, then hulled to remove the
                  outer layers.
                </li>
                <li>
                  Wet Method: Cherries are pulped, fermented, washed, and dried
                  to remove mucilage.
                </li>
              </ul>
            </div>
          </div>
          <div className="my-12 h-[2px] w-full bg-[#D0D0D0]"></div>

          {/* Milling & Sorting */}
          <div className="relative flex flex-col items-center md:flex-row">
            <div className="flex w-full justify-center">
              <Image
                src="/images/milling.svg"
                alt="Milling & Sorting"
                width={400}
                height={400}
                className="h-auto max-w-full object-contain"
                quality={100}
              />
            </div>
            <div className="w-full max-w-full">
              <h3 className="cormorant-font mb-2 text-[2rem] md:text-[2.5rem]">
                Milling & Sorting
              </h3>
              <ul className="poppins-regular list-outside list-disc pl-6 text-base leading-[170%]">
                <li>
                  Hull beans to remove parchment layer (if wet-processed).
                </li>
                <li>Sort beans by size, weight, and quality.</li>
                <li>Remove defective beans to ensure premium quality.</li>
              </ul>
            </div>
          </div>
          <div className="my-12 h-[2px] w-full bg-[#D0D0D0]"></div>

          {/* Roasting & Grinding */}
          <div className="relative flex flex-col-reverse items-center gap-12 md:flex-row-reverse">
            <div className="flex w-1/2 justify-center">
              <Image
                src="/images/roasting.svg"
                alt="Roasting & Grinding"
                width={300}
                height={300}
                className="h-auto max-w-full object-contain"
                quality={100}
              />
            </div>
            <div className="w-full max-w-full">
              <h3 className="cormorant-font mb-2 text-[2rem] md:text-[2.5rem]">
                Roasting & Grinding
              </h3>
              <ul className="poppins-regular list-outside list-disc pl-6 text-base leading-[170%]">
                <li>Roast beans at specific temperatures to enhance flavor.</li>
                <li>
                  Grind to different levels (coarse, medium, fine) based on
                  brewing methods.
                </li>
              </ul>
            </div>
          </div>
          <div className="my-12 h-[2px] w-full bg-[#D0D0D0]"></div>

          {/* Packaging & Exporting */}
          <div className="flex flex-col items-center gap-12 md:flex-row">
            <div className="flex w-full justify-center">
              <Image
                src="/images/packaging.svg"
                alt="Packaging & Exporting"
                width={300}
                height={300}
                className="h-auto max-w-full object-contain"
                quality={100}
              />
            </div>
            <div className="w-full max-w-full">
              <h3 className="cormorant-font mb-2 text-[2rem] md:text-[2.5rem]">
                Packaging & Exporting
              </h3>
              <ul className="poppins-regular list-outside list-disc pl-6 text-base leading-[170%]">
                <li>
                  Pack green beans in jute bags or vacuum-sealed bags for
                  freshness.
                </li>
                <li>
                  Label and prepare for shipment to coffee roasters worldwide.
                </li>
                <li>Transport via land, air, or sea to global markets.</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <PlantCoffeeSection />
      <Inquiry />
      <Footer />
    </>
  );
}
