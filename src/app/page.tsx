import React from "react";
import About from "@/components/About";
import LandingPage from "@/components/home/<USER>";
import PlantCoffeeSection from "@/components/PlantCoffee.tsx";
// import Production from "@/components/Production";
import PackagedProducts from "@/components/PackagedProducts";
import Inquiry from "@/components/Inquiry";
import Footer from "@/components/Foooter";
import Clients from "@/components/Clients";

// import Image from "next/image";
const HomePage: React.FC = () => {
  return (
    <main className="mx-auto w-full">
      <div className="fixed top-0 left-0 z-0 h-screen w-screen bg-[url('/images/pattern.svg')] bg-cover bg-top opacity-20" />
      {/* <div className=" w-full h-full fixed top-0 left-0 z-0"> 
       <Image
          src="/images/pattern.svg"
          alt="Background Pattern"
          width={0}
          height={0}
          priority
          quality={100}
          className="absolute top-0 left-0 w-full h-auto opacity-20"
        />  */}

      <LandingPage />
      <About />
      <PackagedProducts />
      <PlantCoffeeSection />
      <Clients/>
      <Inquiry />
      <Footer />
    </main>
  );
};

export default HomePage;
