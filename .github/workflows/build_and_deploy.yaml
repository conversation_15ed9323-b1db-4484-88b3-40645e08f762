name: Optisyslab CICD

on:
  push:
    branches:
      - main
      - develop
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Get VM SSH host and user
        id: get_vm_conf
        run: |
          case "${{ github.ref }}" in
          refs/heads/develop)
            export SERVER_IP=${{secrets.SERVER_IP_DEVELOP}}
            export SERVER_USERNAME=${{secrets.SERVER_USERNAME_DEVELOP}}
            export PROJECT_PATH=/srv/docker-images
          ;;
          refs/heads/main)
            export SERVER_IP=${{secrets.SERVER_IP_MAIN}}
            export SERVER_USERNAME=${{secrets.SERVER_USERNAME_MAIN}}
            export PROJECT_PATH=/srv/docker-images
          ;;
          esac
          echo "SERVER_IP=${SERVER_IP}" >> $GITHUB_OUTPUT
          echo "SERVER_USERNAME=${SERVER_USERNAME}" >> $GITHUB_OUTPUT
          echo "PROJECT_PATH=${PROJECT_PATH}" >> $GITHUB_OUTPUT

      - name: Configure SSH key
        run: |
          mkdir -p ~/.ssh
          touch ~/.ssh/id_rsa
          chmod 666 ~/.ssh/id_rsa
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 400 ~/.ssh/id_rsa
          ssh-keyscan ${{ steps.get_vm_conf.outputs.SERVER_IP }} >> ~/.ssh/known_hosts

      - name: Deploy to Server
        run: |
          make build-and-save-standalone
          scp -r sherpa-sips-latest.tar ${{ steps.get_vm_conf.outputs.SERVER_USERNAME }}@${{ steps.get_vm_conf.outputs.SERVER_IP }}:${{ steps.get_vm_conf.outputs.PROJECT_PATH }}
          ssh ${{ steps.get_vm_conf.outputs.SERVER_USERNAME }}@${{ steps.get_vm_conf.outputs.SERVER_IP }} "docker stop sherpa-sips || true"
          ssh ${{ steps.get_vm_conf.outputs.SERVER_USERNAME }}@${{ steps.get_vm_conf.outputs.SERVER_IP }} "docker rm sherpa-sips || true"
          ssh ${{ steps.get_vm_conf.outputs.SERVER_USERNAME }}@${{ steps.get_vm_conf.outputs.SERVER_IP }} "docker load -i ${{ steps.get_vm_conf.outputs.PROJECT_PATH }}/sherpa-sips-latest.tar"
          ssh ${{ steps.get_vm_conf.outputs.SERVER_USERNAME }}@${{ steps.get_vm_conf.outputs.SERVER_IP }} "docker run -d --name sherpa-sips -p 3040:3000 sherpa-sips:latest"
