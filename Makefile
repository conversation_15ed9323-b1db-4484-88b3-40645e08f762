# Makefile for building and transferring Docker images to VPS

# Configuration
IMAGE_NAME ?= sherpa-sips
TAG ?= latest
DOCKERFILE ?= Dockerfile
CONTEXT ?= .
VPS_USER ?= root
VPS_IP ?= your.vps.ip
IMAGE_TARBALL = $(IMAGE_NAME)-$(TAG).tar
REMOTE_DIR ?= /home/<USER>
REGISTRY_PORT ?= 5000

.PHONY: help build build-standalone save-image transfer-image load-image \
        start-registry tag-image push-image pull-image clean

help:
	@echo "Docker Image Management"
	@echo "Usage: make [target]"
	@echo ""
	@echo "Build Targets:"
	@echo "  build           Build Docker image (standard)"
	@echo "  build-standalone Build with Next.js standalone output"
	@echo ""
	@echo "Transfer Targets (SCP Method):"
	@echo "  save-image      Save image to tarball"
	@echo "  transfer-image  Transfer tarball to VPS"
	@echo "  load-image      Load image on VPS"
	@echo ""
	@echo "Transfer Targets (Registry Method):"
	@echo "  start-registry  Start Docker registry on VPS"
	@echo "  tag-image       Tag image for private registry"
	@echo "  push-image      Push image to registry"
	@echo "  pull-image      Pull image on VPS"
	@echo ""
	@echo "Utility Targets:"
	@echo "  clean           Remove local tarball"
	@echo "  all-transfer    Build + transfer using SCP method"

# Build Targets
build:
	docker build -t $(IMAGE_NAME):$(TAG) -f $(DOCKERFILE) $(CONTEXT)
	@echo "Image built: $(IMAGE_NAME):$(TAG)"

build-standalone:
	@echo "Building with standalone output (ensure next.config.js has output: 'standalone')"
	docker build -t $(IMAGE_NAME):$(TAG) -f $(DOCKERFILE) $(CONTEXT) \
		--build-arg STANDALONE=true
	@echo "Standalone image built: $(IMAGE_NAME):$(TAG)"

# SCP Transfer Method
save-image: build
	docker save -o $(IMAGE_TARBALL) $(IMAGE_NAME):$(TAG)
	@echo "Image saved to $(IMAGE_TARBALL)"

transfer-image: save-image
	scp $(IMAGE_TARBALL) $(VPS_USER)@$(VPS_IP):$(REMOTE_DIR)
	@echo "Transferred to VPS at $(REMOTE_DIR)/$(IMAGE_TARBALL)"

load-image:
	ssh $(VPS_USER)@$(VPS_IP) "docker load -i $(REMOTE_DIR)/$(IMAGE_TARBALL) && \
	echo 'Image loaded. Run with:'; \
	echo 'docker run -p 3000:3000 $(IMAGE_NAME):$(TAG)'"

# Registry Transfer Method
start-registry:
	ssh $(VPS_USER)@$(VPS_IP) "docker run -d -p $(REGISTRY_PORT):$(REGISTRY_PORT) \
	--restart=always --name registry registry:2"
	@echo "Registry running at $(VPS_IP):$(REGISTRY_PORT)"

tag-image: build
	docker tag $(IMAGE_NAME):$(TAG) $(VPS_IP):$(REGISTRY_PORT)/$(IMAGE_NAME):$(TAG)
	@echo "Image tagged for private registry"

push-image: tag-image
	docker push $(VPS_IP):$(REGISTRY_PORT)/$(IMAGE_NAME):$(TAG)
	@echo "Image pushed to registry"

pull-image:
	ssh $(VPS_USER)@$(VPS_IP) "docker pull localhost:$(REGISTRY_PORT)/$(IMAGE_NAME):$(TAG)"
	@echo "Image pulled on VPS. Run with:"
	@echo "  docker run -p 3000:3000 localhost:$(REGISTRY_PORT)/$(IMAGE_NAME):$(TAG)"

# Combined Commands
all-transfer: build save-image transfer-image load-image
build-and-save: build save-image
build-and-save-standalone: build-standalone save-image
clean:
	rm -f $(IMAGE_TARBALL)
	@echo "Cleaned local tarball"